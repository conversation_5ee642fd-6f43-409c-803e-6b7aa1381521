import { MVC } from "MVC";

export default class PayModel extends MVC.BaseModel {
    private static _instance: PayModel = null;

    constructor() {
        super();
        if (PayModel._instance == null) {
            PayModel._instance = this;
        }
    }

    public reset(): void {}

    public static get getInstance(): PayModel {
        if (PayModel._instance == null) {
            PayModel._instance = new PayModel();
        }
        return PayModel._instance;
    }
}

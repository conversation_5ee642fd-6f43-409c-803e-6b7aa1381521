import { ListenID } from "ListenID";
import { Notifier } from "Notifier";
import { GameUtil } from "GameUtil";
import { NodePool } from "NodePool";
import { Game, ModeCfg } from "Game";
import NPC from "NPC";
import { TrackManger } from "TrackManger";
import CompManager from "CompManager";

export namespace RewardEvent {
    export enum Type {
        Not = 0,
        LvUPSkill = 1,
        DroppedBuff = 2,
        Skill = 7,
        SkillBuff = 8,
        Buff = 9,
        Role = 20,
        Pet = 21,
        RandomBuild_998 = 998,
        RandomBuild_999 = 999,
        FiringBuff = 1000
    }

    export const RandomBuild: { [key: number]: Type[] } = {
        [Type.RandomBuild_998]: [Type.Skill, Type.SkillBuff],
        [Type.RandomBuild_999]: [Type.Skill, Type.SkillBuff, Type.Buff]
    };

    export const TrackMsg: { [key: number]: any } = {
        [Type.Skill]: {
            trackType: TrackManger.Type.RewardBuild,
            icon: "v1/images/fight/icon/actskill_icon"
        },
        [Type.SkillBuff]: {
            trackType: TrackManger.Type.RewardBuild,
            icon: "v1/images/fight/icon/passskill_icon"
        },
        [Type.Buff]: {
            trackType: TrackManger.Type.RewardBuild,
            icon: "v1/images/fight/icon/passskill_icon"
        },
        [Type.Role]: {
            trackType: TrackManger.Type.RewardRole,
            icon: "img/role/touxiang05"
        },
        [Type.Pet]: {
            trackType: TrackManger.Type.RewardPet,
            icon: "v1/images/fight/icon/pet_fw_icon"
        }
    };

    enum TimeType {
        Order = 1,
        Fix = 2
    }

    interface EventData {
        type: Type;
        time: number;
        timeType: TimeType;
        target?: any;
    }

    interface EventList {
        time: number;
        list: EventData[];
    }

    export class Manager {
        public eventList: { [key: number]: EventList } = {
            [TimeType.Order]: {
                time: 0,
                list: []
            },
            [TimeType.Fix]: {
                time: 0,
                list: []
            }
        };
        public bronMgr: any;
        private _orderTarget: EventData;

        constructor(bronMgr: any) {
            this.bronMgr = bronMgr;
        }

        public get orderEvent(): EventList {
            return this.eventList[TimeType.Order];
        }

        public get fixEvent(): EventList {
            return this.eventList[TimeType.Fix];
        }

        public get game(): any {
            return Game.Mgr.instance;
        }

        public createEventList(): void {
            const skillPoolData = ModeCfg.Skiilpool.get(this.bronMgr.level);
            const elements = JSON.parse(skillPoolData.element);
            
            elements?.forEach((element: any[]) => {
                let eventType = element[0];
                if (eventType > 900) {
                    eventType = GameUtil.getRandomInArray(RandomBuild[eventType], 1)[0];
                }
                
                const eventData: EventData = {
                    type: eventType,
                    time: element[1] + GameUtil.random(-5, 5),
                    timeType: GameUtil.CheckSection(7, eventType, 10) ? TimeType.Order : TimeType.Fix
                };
                
                this.eventList[eventData.timeType].list.push(eventData);
            });

            for (const key in this.eventList) {
                this.eventList[key].list.sort((a, b) => a.time - b.time);
            }
        }

        public onUpdate(deltaTime: number): void {
            if (this.orderEvent.list.length > 0 && !this._orderTarget) {
                this.orderEvent.time += deltaTime;
                if (this.orderEvent.time >= this.orderEvent.list[0].time) {
                    this.createNpc(this.orderEvent.list[0], this.bronMgr.getRandomPos(1500));
                }
            } else {
                if (this._orderTarget && !this._orderTarget.target.isValid) {
                    this._orderTarget = null;
                }
            }

            this.fixEvent.time += deltaTime;
            if (this.fixEvent.list.length > 0 && this.fixEvent.time >= this.fixEvent.list[0].time) {
                this.createNpc(this.fixEvent.list[0], this.bronMgr.getRandomPos(1500));
            }
        }

        public createNpc(eventData: EventData, position: cc.Vec2): Promise<void> {
            return new Promise(() => {
                const event = this.eventList[eventData.timeType].list.splice(0, 1)[0];
                cc.log("创建RewardEvent", event);
                
                NodePool.spawn("entity/fight/RewardEvent_" + event.type).setNodeAssetFinishCall((node: cc.Node) => {
                    const npc = node.getComponent(NPC);
                    node.parent = this.bronMgr.game._entityNode;
                    npc.setPosition(cc.v2(position.x, position.y));
                    npc.init();
                    npc.set(event.type);
                    event.target = npc;
                    this._orderTarget = event;
                    
                    CompManager.Instance.registerComp(npc);
                    Notifier.send(ListenID.Fight_SetTrack, {
                        node: node,
                        ...TrackMsg[eventData.type]
                    });
                });
            });
        }
    }
}

import { CallID } from "CallID";
import { ListenID } from "ListenID";
import { Notifier } from "Notifier";
import { BaseSdk, VideoAdCode } from "BaseSdk";

declare const cc: any;
declare const jsb: any;
declare const window: any;

const nativeAndroid = cc.nativeAndroid = cc.nativeAndroid || {};
const callStaticMethod = window.jsb && jsb.reflection ? jsb.reflection.callStaticMethod : function () {};

export default class NativeAndroid extends BaseSdk {
    public defaultClass: string = "org/cocos2dx/javascript/AppActivity";
    public onbannerShow: Function = function () {};
    private _shareCall: Function = null;
    private _privacyCallback: Function;
    public onPlayEnd: Function;
    public onFullPlayEnd: Function;

    constructor() {
        super();
    }

    public logout(): void {}

    public init(config: any): void {
        super.init(config);
        
        Notifier.changeListener(true, ListenID.Event_SendEvent, this.sendEvent, this);
        Notifier.changeCall(true, CallID.Shop_GetProductList, this.getProductList, this);

        nativeAndroid.bannerShow = () => {
            this.onbannerShow && this.onbannerShow();
        };

        nativeAndroid.bannerShowerr = function () {};

        nativeAndroid.videoClose = () => {
            this.onPlayEnd && this.onPlayEnd(VideoAdCode.NOT_COMPLITE, "未完整观看广告");
        };

        nativeAndroid.videoFinish = () => {
            this.onPlayEnd && this.onPlayEnd(VideoAdCode.COMPLETE, "看完广告");
        };

        nativeAndroid.videoError = () => {
            console.error("[WxAdCtrler][showVideoAD] error");
            this.onPlayEnd && this.onPlayEnd(VideoAdCode.AD_ERROR, "内容正在加载中，请稍后再试");
        };

        nativeAndroid.rewardVideoSuccess = () => {
            this.onPlayEnd && this.onPlayEnd(VideoAdCode.SHOW_SUCCESS, "");
        };

        nativeAndroid.fullVideoSuccess = () => {
            this.onFullPlayEnd && this.onFullPlayEnd(VideoAdCode.SHOW_SUCCESS, "");
        };

        nativeAndroid.fullVideoHide = () => {
            this.onFullPlayEnd && this.onFullPlayEnd(VideoAdCode.COMPLETE, "");
        };

        nativeAndroid.fullVideoError = () => {
            this.onFullPlayEnd && this.onFullPlayEnd(VideoAdCode.AD_ERROR, "");
        };

        nativeAndroid.onPrivacyAccept = () => {
            this.sendEvent("confirm_privacy", "none");
            this._privacyCallback && this._privacyCallback(true);
        };

        nativeAndroid.onPrivacyReject = () => {
            this._privacyCallback && this._privacyCallback(false);
        };

        nativeAndroid.shareResult = (result: number) => {
            if (result === 0) {
                this._shareCall && this._shareCall(0);
            } else {
                this._shareCall && this._shareCall(1);
            }
        };

        nativeAndroid.onPurchaseSuccess = (productId: string) => {
            cc.game.emit("payFinish", 200, productId);
        };

        nativeAndroid.onPurchaseFail = (productId: string) => {
            cc.game.emit("payFinish", 0, productId);
        };

        nativeAndroid.onPurchasedProductsFetched = (data: string) => {
            cc.log(data);
        };

        callStaticMethod(this.defaultClass, "checkPurchasesInApp", "()V");
    }

    public login(callback?: Function): Promise<any> {
        return new Promise((resolve) => {
            callback && callback(null);
            resolve(null);
        });
    }

    public showBannerWithNode(adUnitId: string, node: cc.Node, callback?: Function): void {
        this.showBannerWithStyle(adUnitId, {}, callback);
    }

    public showBannerWithStyle(adUnitId: string, style: any, callback?: Function): void {
        this.onbannerShow = callback;
    }

    public hideBanner(): void {}

    public destroyBanner(): void {}

    public showVideoAD(adUnitId: string, callback: Function): void {
        this.onPlayEnd = callback;
        callStaticMethod(this.defaultClass, "showVideo", "(Ljava/lang/String;)V", adUnitId);
    }

    public showFullVideoAD(adUnitId: string, callback: Function): void {
        this.onFullPlayEnd = callback;
        callStaticMethod(this.defaultClass, "showFullVideo", "(Ljava/lang/String;)V", adUnitId);
    }

    public sendEvent(eventName: string, eventData: any): void {
        const eventKey = eventName;
        if (eventData == null || eventData === "" || eventData === "none") {
            eventData = "{}";
        }
        
        if (eventName === "reward_btn" && cc.sys.getNetworkType() === cc.sys.NetworkType.NONE) {
            return;
        }
        
        console.log("cc", "sendMsg", JSON.stringify(eventData));
        callStaticMethod(this.defaultClass, "sendMsg", "(Ljava/lang/String;Ljava/lang/String;)V", eventKey, JSON.stringify(eventData));
    }

    public vibrate(type: number = 0): void {
        callStaticMethod(this.defaultClass, "vibrate", "(I)V", type === 0 ? 10 : 300);
    }

    public share(): void {}

    public showInsertAd(): void {}

    public showSplashAd(adUnitId: string): void {
        callStaticMethod(this.defaultClass, "showSplashAd", "(Ljava/lang/String;)V", adUnitId);
    }

    public showPrivacy(callback: Function): void {
        this._privacyCallback = callback;
        callback && callback(true);
    }

    public goRate(): void {
        callStaticMethod(this.defaultClass, "requestReview", "()V");
    }

    public showDebugAdView(): void {
        callStaticMethod(this.defaultClass, "debugAdView", "(Ljava/lang/String;)V", "test");
    }

    public toPay(payData: any): void {
        callStaticMethod(this.defaultClass, "purchase", "(Ljava/lang/String;)V", payData.goodsId + "");
    }

    public toSubscribe(payData: any): void {
        callStaticMethod(this.defaultClass, "subscribe", "(Ljava/lang/String;)V", payData.goodsId + "");
    }

    public getProductList(): any {
        const result = callStaticMethod(this.defaultClass, "getProductList", "()Ljava/lang/String;");
        console.log("[getProductList]" + typeof result);
        console.log(result);
        
        const productList = typeof result === "string" ? JSON.parse(result) : result;
        Notifier.send(ListenID.Shop_ShopItemList, productList);
        return productList;
    }

    public toShareFaceBook(callback: Function): void {
        this._shareCall = callback;
        callStaticMethod(this.defaultClass, "toShareFaceBook", "()V");
    }
}

import { MovingBGSprite } from "./MovingBGSprite";
import { CallID } from "./CallID";
import { Cfg } from "./Cfg";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { GameUtil } from "./GameUtil";
import { KnapsackVo } from "./KnapsackVo";
import { RecordVo } from "./RecordVo";
import { Game } from "./Game";
import { BronMonsterManger } from "./BronMonsterManger";
import { CompManager } from "./CompManager";
import { NodePool } from "./NodePool";
import { ModeChainsModel } from "./ModeChainsModel";
import { M33_TestBox } from "./M33_TestBox";
import { MCBoss } from "./MCBoss";
import { MCDragoMutilation } from "./MCDragoMutilation";
import { MCDragon } from "./MCDragon";
import { MCPet } from "./MCPet";
import { MCRole } from "./MCRole";

export namespace MChains {
    export enum RoundStatus {
        NONE = 0,
        BATTLE = 1,
        TRANSITIONAM = 2,
        BOSSCOMEON = 3,
        END = 4
    }

    export enum PassType {
        Forward = 0,
        D360 = 1,
        Move = 2,
        ForwardMoveExtend = 3
    }

    export enum poolType {
        NormalBuff = 1,
        HighBuff = 2
    }

    export class RecordData extends RecordVo.Data {
        public poolADMap: any = {
            [poolType.NormalBuff]: {
                resetNum: 3,
                getAll: 1
            },
            [poolType.HighBuff]: {
                resetNum: 1,
                getAll: 0
            }
        };
        public freeTime: number = 1;
        public killNum: number = 0;
        public countdownTime: number = 0;
        public adRefreshEquip: number = 9999;
        public ADNum: number = 0;
        public testNum: number = 1;
    }

    export class Mgr extends Game.Mgr {
        public cameraZoomRatio: number = 1;
        public recordVo: RecordVo.Mgr;
        public passType: PassType = PassType.Forward;
        public passParam: any;
        private _mainRole: MCRole;

        constructor(passParam: any) {
            super(passParam);
            this.recordVo = new RecordVo.Mgr("MChains", () => {
                return new RecordData();
            });
            this.passParam = passParam;
        }

        get mode(): ModeChainsModel {
            return ModeChainsModel.default.instance;
        }

        get rVo(): RecordData {
            return this.recordVo.vo as RecordData;
        }

        public loadMap(gameNode: cc.Node, finishCall?: Function): void {
            this.gameNode = gameNode;
            this.miniGameCfg = Cfg.MiniGameLv.get(this.passParam.id);
            this.mode.miniGameCfg = this.miniGameCfg;
            this._entityNode = gameNode.getChildByName("entityNode");
            this._mapNode = gameNode.getChildByName("mapNode");
            this._bulletNode = gameNode.getChildByName("bulletNode");
            this._topEffectNode = gameNode.getChildByName("topEffectNode");
            this.LifeBarUI = gameNode.getORaddChildByName("LifeBarUI");
            this.topUINode = gameNode.getORaddChildByName("topUINode");
            this._botEffectNode = gameNode.getChildByName("botEffectNode");
            this.behitUI = gameNode.getORaddChildByName("behitUI");
            this._finishCall = finishCall;

            Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then((mapNode: cc.Node) => {
                mapNode.parent = this._mapNode;
                mapNode.zIndex = -1;
                this.myTiledMap = this._mapNode.getComByChild(cc.TiledMap);
                this.createRole(cc.v2(0, 90), this.miniGameCfg.roleId).then(() => {});
                this.gameCamera.lookPos.set(cc.v2(0, GameUtil.getDesignSize.height / 2));
                this.sendEvent("Start");
            });

            this.gameCamera.setZoomRatio(this.cameraZoomRatio);
            this.gameCamera.setPosition(cc.Vec2.ZERO);
            this.bronMonsterMgr = this.gameNode.getORaddComponent(SpawningMgr);
            this.bronMonsterMgr.init();
            this.topUINode.active = !!this.mode.bShowMonsterBlob(this.miniGameCfg);
            
            if (this.mode.bShowMatrix(this.miniGameCfg)) {
                Manager.setGroupMatrixByStr("Monsetr", "Role", true);
            }
            
            this.scenceSize = [
                -375 / this.gameCamera.cutZoomRatio * 0.8,
                375 / this.gameCamera.cutZoomRatio * 0.8,
                0,
                0
            ];
            this._finishCall && this._finishCall();
        }

        public createRole(position: cc.Vec2, roleId?: number): Promise<MCRole> {
            return new Promise((resolve) => {
                NodePool.spawn("entity/fight/ModeChains/roleMod").setNodeAssetFinishCall((roleNode: cc.Node) => {
                    const role = roleNode.getComponent(MCRole.default);
                    roleNode.parent = this._entityNode;
                    role.setPosition(position);
                    role.init();
                    role._logicTime = 0;
                    if (roleId) {
                        role.roleId = roleId;
                    }
                    role.setRole();
                    CompManager.default.Instance.registerComp(role);
                    this.mainRole = role;
                    resolve(role);
                });
            });
        }

        public gameEnd(): void {
            Game.timerOnce(() => {
                this.gameState = Game.State.NONE;
            }, 0.5);
        }

        get mainRole(): MCRole {
            return this._mainRole;
        }

        set mainRole(role: MCRole) {
            this._mainRole = role;
        }

        public gamePause(isPause: boolean): void {
            super.gamePause(isPause);
            Manager.setPhysics(!isPause);
        }

        public onUpdate(dt: number): void {
            super.onUpdate(dt);
        }

        public setTestMode(isOpen: boolean): void {
            if (this._mapTestMode) {
                this._mapTestMode.open(isOpen);
            } else {
                Manager.loader.loadPrefab("ui/ModeChains/M33_TestBox").then((testBoxNode: cc.Node) => {
                    testBoxNode.setParent(this.gameNode);
                    this._mapTestMode = testBoxNode.getORaddComponent(M33_TestBox.default);
                });
            }
        }
    }

    export class SpawningMgr extends BronMonsterManger {
        private _batchNum: number = 0;
        public cutStatus: RoundStatus = RoundStatus.NONE;
        public countDown: number = 0;

        get batchNum(): number {
            return this._batchNum;
        }

        set batchNum(value: number) {
            this._batchNum = value;
            if (value !== 0) {
                Notifier.send(ListenID.Fight_GameRound, value);
                this._batchSumCount = 0;
                this.RoundMonster = this.MonsterLv.filter((monster: any) => monster.round === this._batchNum);
                this.RoundMonster.forEach((monster: any) => {
                    this._batchSumCount += monster.sumCount;
                });
            }
        }

        get game(): Mgr {
            return Game.mgr as Mgr;
        }

        get mode(): ModeChainsModel {
            return ModeChainsModel.default.instance;
        }

        public init(): void {
            this.MonsterLv = JSON.parse(JSON.stringify(Game.ModeCfg.MonsterLv.filter({
                lv: this.game.miniGameCfg.lvid
            })));
            this.batchNum++;
        }

        public onUpdate(dt: number): void {
            if (this.game.gameState !== Game.State.PAUSE) {
                if (this.cutStatus === RoundStatus.BATTLE) {
                    this._dtTime += dt;
                    if (this._dtTime >= 1) {
                        this._dtTime = 0;
                    }
                    if (this._batchSumCount > 0) {
                        this.checkAddMonster(dt);
                    }
                    this.checkMonDe();
                }
            }
        }

        public checkAddMonster(dt: number): void {
            if (this.mainRole && !this.mainRole.isDead && !this.isBossRound) {
                this._RoundMonster.forEach((monster: any) => {
                    monster.letTime += dt;
                    if (monster.createNum < monster.sumCount && monster.letTime > monster.bronTime) {
                        monster.letTime = 0;
                        monster.createNum++;
                        this.addMonsterById(monster, null);
                    }
                });
            }
        }

        public addMonsterById(monsterCfg: any, position?: cc.Vec2): void {
            this.createMonster(monsterCfg, position || this.randomPos).then((monster: any) => {
                monster.toMove();
            });
            this._batchSumCount--;
        }

        public checkMonDe(): void {
            if (this._batchSumCount <= 0 && this.survivalMonsterNum === 0) {
                Notifier.send(ListenID.Fight_End, true);
            }
        }

        get survivalMonsterNum(): number {
            return this.game.monsterMap.size;
        }

        get randomPos(): cc.Vec2 {
            return cc.v2(
                Game.random(this.game.scenceSize[0], this.game.scenceSize[1]),
                this.maxLen + 200
            );
        }

        get maxLen(): number {
            return GameUtil.getDesignSize.height / this.game.gameCamera.cutZoomRatio;
        }

        public changeGameStatus(status: RoundStatus): void {
            switch (status) {
                case RoundStatus.BATTLE:
                    break;
                case RoundStatus.BOSSCOMEON:
                    break;
                case RoundStatus.END:
                    break;
            }
            Notifier.send(ListenID.Fight_GameRoundType, status);
            this.cutStatus = status;
        }
    }
}

import { Api } from "Api";
import { getServerTime, getClientTime, getSign } from "function";
import TimeManage from "TimeManage";
import { LIMIT_REPORT_COUNT, LIMIT_REPORT_INTERVAL } from "config";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";

declare const window: any;

class ReportService {
    public click(): void {
        if (window.params.getShowParams("w_cid") || window.params.getShowParams("clickid")) {
            let reportData = {
                yw_track_channel: "tt_ttmn",
                yw_app_name: window.params.getGameParams("app_name"),
                yw_channel: window.params.getGameParams("channel"),
                yw_version: window.params.getGameParams("version"),
                yw_opi: window.params.getUserParams("openid")
            };
            
            reportData = Object.assign(reportData, window.params.getShowParams());
            window.Logger.info("## 上报监测数据", reportData);
            
            Api.click(reportData, 
                (result: any) => {
                    window.Logger.info("## 上报监测数据完成", result);
                },
                (error: any) => {
                    window.Logger.error("## 监测数据上报失败;", error);
                },
                () => {}
            );
        }
    }

    public active(): void {
        getServerTime((serverTime: any) => {
            const eventData = [{
                d_type: "u_op",
                t: getClientTime(),
                act: "active",
                wds: "激活",
                clickid: window.params.getShowParams("clickid")
            }];
            
            let reportData = {
                app_name: window.params.getGameParams("app_name"),
                channel: window.params.getGameParams("channel"),
                version: window.params.getGameParams("version"),
                uuid: window.params.getUserParams("openid"),
                m_data: JSON.stringify(eventData),
                b_t: serverTime.data.data.time
            };
            
            reportData = getSign(reportData);
            window.Logger.info("## 上报激活", reportData);
            
            Api.report(reportData, 
                () => {},
                (error: any) => {
                    window.Logger.error("## 激活上报失败;", error);
                },
                () => {}
            );
        });
    }

    public online(): void {
        const endTime = TimeManage.getEndClientTime();
        const onlineTime = TimeManage.getOnlineTime();
        TimeManage.removeStartClientTime();
        TimeManage.removeEndClientTime();
        
        getServerTime((serverTime: any) => {
            const eventData = [{
                d_type: "u_op",
                t: endTime,
                act: "time",
                wds: "时长",
                tab1: onlineTime,
                clickid: window.params.getShowParams("clickid")
            }];
            
            let reportData = {
                app_name: window.params.getGameParams("app_name"),
                channel: window.params.getGameParams("channel"),
                version: window.params.getGameParams("version"),
                uuid: window.params.getUserParams("openid"),
                m_data: JSON.stringify(eventData),
                b_t: serverTime.data.data.time
            };
            
            reportData = getSign(reportData);
            window.Logger.info("## 上报在线时长", reportData);
            
            Api.report(reportData, 
                () => {},
                (error: any) => {
                    window.Logger.error("## 在线时长上报失败;", error);
                },
                () => {}
            );
        });
    }

    public userInfo(): void {
        getServerTime((serverTime: any) => {
            const eventData = [{
                d_type: "u",
                platform: window.params.getGameParams("channel"),
                t: getClientTime(),
                clickid: window.params.getShowParams("clickid"),
                requestid: window.params.getShowParams("requestid"),
                os: window.params.getShowParams("os"),
                imei: window.params.getShowParams("imei"),
                android_id: window.params.getShowParams("android_id"),
                mac: window.params.getShowParams("mac"),
                idfa: window.params.getShowParams("idfa"),
                idfa_md5: window.params.getShowParams("idfa_md5"),
                d_m: window.params.getShowParams("d_m"),
                idfv: window.params.getShowParams("idfv"),
                oaid: window.params.getShowParams("oaid")
            }];
            
            let reportData = {
                app_name: window.params.getGameParams("app_name"),
                channel: window.params.getGameParams("channel"),
                version: window.params.getGameParams("version"),
                uuid: window.params.getUserParams("openid"),
                m_data: JSON.stringify(eventData),
                b_t: serverTime.data.data.time
            };
            
            reportData = getSign(reportData);
            window.Logger.info("## 上报用户信息", reportData);
            
            Api.report(reportData, 
                (result: any) => {
                    window.Logger.info("## 上报用户信息成功;", result);
                },
                (error: any) => {
                    window.Logger.error("## 上报用户信息失败;", error);
                },
                () => {}
            );
            
            Notifier.send(ListenID.Event_LoginTA, reportData.uuid);
        });
    }

    public adRequest(adType: string): void {
        const eventData = {
            d_type: "ad_ac",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: "ad_request",
            wds: "广告请求",
            ad_t: adType,
            tab1: "字节"
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public adFill(adType: string): void {
        const eventData = {
            d_type: "ad_ac",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: "ad_fill",
            wds: "广告填充",
            ad_t: adType,
            tab1: "字节"
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public adClick(adType: string): void {
        const eventData = {
            d_type: "ad_ac",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: "ad_click",
            wds: "广告触发",
            ad_t: adType,
            tab1: "字节"
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public adImpression(adType: string): void {
        const eventData = {
            d_type: "ad_ac",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: "ad_impression",
            wds: "广告展示",
            ad_t: adType,
            tab1: "字节"
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public adImpressionDone(adType: string): void {
        const eventData = {
            d_type: "ad_ac",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: "ad_impression_done",
            wds: "广告播放完成",
            ad_t: adType,
            tab1: "字节"
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public other(action: string, description: string): void {
        const eventData = {
            d_type: "u_op",
            t: getClientTime(),
            clickid: window.params.getShowParams("clickid"),
            act: action,
            wds: description
        };
        
        window.reportQueue.push(eventData);
        this.checkReportQueue();
    }

    public checkReportQueue(shouldSetTime: boolean = true): void {
        if (shouldSetTime && !TimeManage.getFirstReportServerTime()) {
            TimeManage.setFirstReportServerTime();
            TimeManage.setFirstReportClientTime();
        }
        
        const queueLength = window.reportQueue.len();
        const timeDiff = getClientTime() - TimeManage.getFirstReportClientTime();
        const shouldReport = queueLength >= LIMIT_REPORT_COUNT || 
                           (TimeManage.getFirstReportClientTime() && timeDiff >= LIMIT_REPORT_INTERVAL);
        
        window.Logger.info(
            "## 当前事件队列情况;",
            `事件队列总数（${queueLength}）（限制数为：${LIMIT_REPORT_COUNT}）`,
            `距离首个事件时长（${timeDiff}）（限制时长为：${LIMIT_REPORT_INTERVAL}）`,
            TimeManage.getFirstReportClientTime(),
            shouldReport
        );
        
        if (queueLength > 0 && shouldReport) {
            setTimeout(() => {
                this.reportQueue();
            }, 500);
        }
    }

    public reportQueue(count: number = 0): void {
        if (Object.keys(window.params.getGameParams()).length <= 0 || !window.params.getUserParams("openid")) {
            return;
        }
        
        const eventList = window.reportQueue.range(0, count === 0 ? window.reportQueue.len() : count);
        let reportData = {
            app_name: window.params.getGameParams("app_name"),
            channel: window.params.getGameParams("channel"),
            version: window.params.getGameParams("version"),
            uuid: window.params.getUserParams("openid"),
            m_data: JSON.stringify(eventList),
            b_t: TimeManage.getFirstReportServerTime()
        };
        
        TimeManage.removeFirstReportServerTime();
        TimeManage.removeFirstReportClientTime();
        reportData = getSign(reportData);
        
        window.Logger.info("## 上报事件队列", reportData, eventList);
        
        Api.report(reportData, 
            () => {},
            (error: any) => {
                window.Logger.error("## 事件队列上报失败;", error);
            },
            () => {}
        );
    }
}

export default new ReportService();

import Res<PERSON>eeper from "ResKeeper";
import { assetLoader } from "AssetLoader";

export class ResUtil {
    public static getResKeeper(node: cc.Node, autoCreate: boolean): any {
        if (node) {
            const resKeeper = node.getComponent(ResKeeper);
            if (resKeeper) {
                return resKeeper;
            } else if (autoCreate) {
                return node.addComponent(ResKeeper);
            } else {
                return ResUtil.getResKeeper(node.parent, autoCreate);
            }
        } else {
            return assetLoader.getResKeeper();
        }
    }
}

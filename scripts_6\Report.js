Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Api = require("Api");
var $2function = require("function");
var $2TimeManage = require("TimeManage");
var $2config = require("config");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var l = function () {
  function e() {}
  e.prototype.click = function () {
    if (window.params.getShowParams("w_cid") || window.params.getShowParams("clickid")) {
      var e = {
        yw_track_channel: "tt_ttmn",
        yw_app_name: window.params.getGameParams("app_name"),
        yw_channel: window.params.getGameParams("channel"),
        yw_version: window.params.getGameParams("version"),
        yw_opi: window.params.getUserParams("openid")
      };
      e = Object.assign(e, window.params.getShowParams());
      window.Logger.info("## 上报监测数据", e);
      $2Api.click(e, function (e) {
        window.Logger.info("## 上报监测数据完成", e);
      }, function (e) {
        window.Logger.error("## 监测数据上报失败;", e);
      }, function () {});
    }
  };
  e.prototype.active = function () {
    $2function.getServerTime(function (e) {
      var t = [{
        d_type: "u_op",
        t: $2function.getClientTime(),
        act: "active",
        wds: "激活",
        clickid: window.params.getShowParams("clickid")
      }];
      var o = {
        app_name: window.params.getGameParams("app_name"),
        channel: window.params.getGameParams("channel"),
        version: window.params.getGameParams("version"),
        uuid: window.params.getUserParams("openid"),
        m_data: JSON.stringify(t),
        b_t: e.data.data.time
      };
      o = $2function.getSign(o);
      window.Logger.info("## 上报激活", o);
      $2Api.report(o, function () {}, function (e) {
        window.Logger.error("## 激活上报失败;", e);
      }, function () {});
    });
  };
  e.prototype.online = function () {
    var e = $2TimeManage.default.getEndClientTime();
    var t = $2TimeManage.default.getOnlineTime();
    $2TimeManage.default.removeStartClientTime();
    $2TimeManage.default.removeEndClientTime();
    $2function.getServerTime(function (o) {
      var r = [{
        d_type: "u_op",
        t: e,
        act: "time",
        wds: "时长",
        tab1: t,
        clickid: window.params.getShowParams("clickid")
      }];
      var a = {
        app_name: window.params.getGameParams("app_name"),
        channel: window.params.getGameParams("channel"),
        version: window.params.getGameParams("version"),
        uuid: window.params.getUserParams("openid"),
        m_data: JSON.stringify(r),
        b_t: o.data.data.time
      };
      a = $2function.getSign(a);
      window.Logger.info("## 上报在线时长", a);
      $2Api.report(a, function () {}, function (e) {
        window.Logger.error("## 在线时长上报失败;", e);
      }, function () {});
    });
  };
  e.prototype.userInfo = function () {
    $2function.getServerTime(function (e) {
      var t = [{
        d_type: "u",
        platform: window.params.getGameParams("channel"),
        t: $2function.getClientTime(),
        clickid: window.params.getShowParams("clickid"),
        requestid: window.params.getShowParams("requestid"),
        os: window.params.getShowParams("os"),
        imei: window.params.getShowParams("imei"),
        android_id: window.params.getShowParams("android_id"),
        mac: window.params.getShowParams("mac"),
        idfa: window.params.getShowParams("idfa"),
        idfa_md5: window.params.getShowParams("idfa_md5"),
        d_m: window.params.getShowParams("d_m"),
        idfv: window.params.getShowParams("idfv"),
        oaid: window.params.getShowParams("oaid")
      }];
      var o = {
        app_name: window.params.getGameParams("app_name"),
        channel: window.params.getGameParams("channel"),
        version: window.params.getGameParams("version"),
        uuid: window.params.getUserParams("openid"),
        m_data: JSON.stringify(t),
        b_t: e.data.data.time
      };
      o = $2function.getSign(o);
      window.Logger.info("## 上报用户信息", o);
      $2Api.report(o, function (e) {
        window.Logger.info("## 上报用户信息成功;", e);
      }, function (e) {
        window.Logger.error("## 上报用户信息失败;", e);
      }, function () {});
      $2Notifier.Notifier.send($2ListenID.ListenID.Event_LoginTA, o.uuid);
    });
  };
  e.prototype.adRequest = function (e) {
    var t = {
      d_type: "ad_ac",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: "ad_request",
      wds: "广告请求",
      ad_t: e,
      tab1: "字节"
    };
    window.reportQueue.push(t);
    this.checkReportQueue();
  };
  e.prototype.adFill = function (e) {
    var t = {
      d_type: "ad_ac",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: "ad_fill",
      wds: "广告填充",
      ad_t: e,
      tab1: "字节"
    };
    window.reportQueue.push(t);
    this.checkReportQueue();
  };
  e.prototype.adClick = function (e) {
    var t = {
      d_type: "ad_ac",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: "ad_click",
      wds: "广告触发",
      ad_t: e,
      tab1: "字节"
    };
    window.reportQueue.push(t);
    this.checkReportQueue();
  };
  e.prototype.adImpression = function (e) {
    var t = {
      d_type: "ad_ac",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: "ad_impression",
      wds: "广告展示",
      ad_t: e,
      tab1: "字节"
    };
    window.reportQueue.push(t);
    this.checkReportQueue();
  };
  e.prototype.adImpressionDone = function (e) {
    var t = {
      d_type: "ad_ac",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: "ad_impression_done",
      wds: "广告播放完成",
      ad_t: e,
      tab1: "字节"
    };
    window.reportQueue.push(t);
    this.checkReportQueue();
  };
  e.prototype.other = function (e, t) {
    var o = {
      d_type: "u_op",
      t: $2function.getClientTime(),
      clickid: window.params.getShowParams("clickid"),
      act: e,
      wds: t
    };
    window.reportQueue.push(o);
    this.checkReportQueue();
  };
  e.prototype.checkReportQueue = function (e) {
    var t = this;
    undefined === e && (e = true);
    if (e && !$2TimeManage.default.getFirstReportServerTime()) {
      $2TimeManage.default.setFirstReportServerTime();
      $2TimeManage.default.setFirstReportClientTime();
    }
    window.Logger.info("## 当前事件队列情况;", "事件队列总数（" + window.reportQueue.len() + "）（限制数为：" + $2config.LIMIT_REPORT_COUNT + "）", "距离首个事件时长（" + ($2function.getClientTime() - $2TimeManage.default.getFirstReportClientTime()) + "）（限制时长为：" + $2config.LIMIT_REPORT_INTERVAL + "）", $2TimeManage.default.getFirstReportClientTime(), window.reportQueue.len() >= $2config.LIMIT_REPORT_COUNT || $2TimeManage.default.getFirstReportClientTime() && $2function.getClientTime() - $2TimeManage.default.getFirstReportClientTime() >= $2config.LIMIT_REPORT_INTERVAL);
    window.reportQueue.len() > 0 && (window.reportQueue.len() >= $2config.LIMIT_REPORT_COUNT || $2TimeManage.default.getFirstReportClientTime() && $2function.getClientTime() - $2TimeManage.default.getFirstReportClientTime() >= $2config.LIMIT_REPORT_INTERVAL) && setTimeout(function () {
      t.reportQueue();
    }, 500);
  };
  e.prototype.reportQueue = function (e) {
    undefined === e && (e = 0);
    if (!(Object.keys(window.params.getGameParams()).length <= 0) && window.params.getUserParams("openid")) {
      var t = window.reportQueue.range(0, 0 === e ? window.reportQueue.len() : e);
      var o = {
        app_name: window.params.getGameParams("app_name"),
        channel: window.params.getGameParams("channel"),
        version: window.params.getGameParams("version"),
        uuid: window.params.getUserParams("openid"),
        m_data: JSON.stringify(t),
        b_t: $2TimeManage.default.getFirstReportServerTime()
      };
      $2TimeManage.default.removeFirstReportServerTime();
      $2TimeManage.default.removeFirstReportClientTime();
      o = $2function.getSign(o);
      window.Logger.info("## 上报事件队列", o, t);
      $2Api.report(o, function () {}, function (e) {
        window.Logger.error("## 事件队列上报失败;", e);
      }, function () {});
    }
  };
  return e;
}();
exports.default = new l();
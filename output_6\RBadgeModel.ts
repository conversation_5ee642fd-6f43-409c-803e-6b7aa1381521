import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { Time } from "Time";
import { GameUtil } from "GameUtil";

export namespace RBadge {
    export enum Key {
        Shop = 1000,
        Role = 1001,
        Fight = 1002,
        Equip = 1003,
        Activity = 1004,
        Task = 1005,
        Shop_FreeDiamond = 1006,
        Shop_FreeCoin = 1007,
        Fight_NavReward = 1008,
        Fight_DeskReward = 1009,
        Equip_FragmentUpgrade = 1010,
        Role_FragmentUpgrade = 1011,
        Activity_Challenge = 1012,
        Activity_Challenge_Num = 1013,
        Activity_Challenge_Reward = 1014,
        Activity_ChallengeCoin = 1015,
        Activity_ChallengeCoin_Num = 1016,
        Activity_ChallengeCoin_Reward = 1017,
        TaskDay = 1018,
        TaskWeek = 1019,
        TaskAchieve = 1020,
        DayTaskBox = 1021,
        DayTaskItem = 1022,
        WeekTaskBox = 1023,
        WeekTaskItem = 1024
    }

    export const Tree: { [key: number]: Key[] } = {
        [Key.Shop]: [Key.Shop_FreeCoin, Key.Shop_FreeDiamond],
        [Key.Equip]: [Key.Equip_FragmentUpgrade],
        [Key.Role]: [Key.Role_FragmentUpgrade],
        [Key.Fight]: [Key.Fight_NavReward, Key.Fight_DeskReward],
        [Key.Activity]: [Key.Activity_Challenge, Key.Activity_ChallengeCoin],
        [Key.Activity_Challenge]: [Key.Activity_Challenge_Num, Key.Activity_Challenge_Reward],
        [Key.Activity_ChallengeCoin]: [Key.Activity_ChallengeCoin_Num, Key.Activity_ChallengeCoin_Reward],
        [Key.Task]: [Key.TaskDay, Key.TaskWeek, Key.TaskAchieve],
        [Key.TaskDay]: [Key.DayTaskBox, Key.DayTaskItem],
        [Key.TaskWeek]: [Key.WeekTaskBox, Key.WeekTaskItem]
    };

    export class Data {
        public key: Key;
        public id: number;
        public children: Data[] = [];
        public isTree: boolean;
        private _parent: Data;

        constructor(data: any) {
            for (let key in data) {
                this[key] = data[key];
            }
            this.children = [];
            this.isTree = !!Tree[this.key];
        }

        public get parent(): Data {
            return this._parent;
        }

        public set parent(value: Data) {
            this._parent = value;
            this._parent.children.push(this);
        }

        public get childCont(): number {
            return this.children.length;
        }

        public unuse(): void {
            if (this.parent) {
                GameUtil.deleteArrItem(this.parent.children, this);
            }
        }
    }
}

export default class RBadgeModel extends MVC.BaseModel {
    private static _instance: RBadgeModel = null;
    public pointList: RBadge.Data[] = [];
    private sTimer: any;

    constructor() {
        super();
        if (RBadgeModel._instance == null) {
            RBadgeModel._instance = this;
        }
    }

    public reset(): void {}

    public static get instance(): RBadgeModel {
        if (RBadgeModel._instance == null) {
            RBadgeModel._instance = new RBadgeModel();
        }
        return RBadgeModel._instance;
    }

    public setPoint(key: RBadge.Key, isShow: boolean, id: number = 0): void {
        let item = this.getItem(key, id);
        
        if (isShow) {
            if (!item) {
                let parentItem: RBadge.Data;
                let path = this.getPath(key);
                path = path.slice(0, path.length - 1);
                let pathItems: RBadge.Data[] = [];
                
                path.forEach((pathKey, index) => {
                    if (index === 0) {
                        parentItem = this.pointList.find(item => item.key === pathKey);
                    } else {
                        parentItem = parentItem.children.find(item => item.key === pathKey);
                    }
                    
                    if (!parentItem) {
                        const prevKey = path[index - 1];
                        parentItem = new RBadge.Data({
                            key: pathKey,
                            id: 0
                        });
                        
                        if (prevKey) {
                            parentItem.parent = pathItems[index - 1];
                        } else {
                            this.pointList.push(parentItem);
                        }
                    }
                    pathItems.push(parentItem);
                });
                
                item = new RBadge.Data({
                    key: key,
                    id: id
                });
                item.parent = pathItems[pathItems.length - 1];
                pathItems.push(item);
            }
        } else if (item) {
            item.unuse();
            item = null;
        }
        
        this.updatBadge();
    }

    public getItem(key: RBadge.Key, id: number = 0): RBadge.Data {
        let currentItem: RBadge.Data;
        
        this.getPath(key).forEach((pathKey, index) => {
            if (index === 0) {
                currentItem = this.pointList.find(item => item.key === pathKey);
            } else {
                if (currentItem) {
                    if (pathKey === key) {
                        currentItem = currentItem.children.find(item => item.key === pathKey && item.id === id);
                    } else {
                        currentItem = currentItem.children.find(item => item.key === pathKey);
                    }
                }
            }
        });
        
        return currentItem;
    }

    public getParent(key: RBadge.Key): RBadge.Key {
        for (let parentKey in RBadge.Tree) {
            if (RBadge.Tree[parentKey].includes(key)) {
                return +parentKey as RBadge.Key;
            }
        }
        return null;
    }

    public getPath(key: RBadge.Key): RBadge.Key[] {
        const path = [key];
        let parent = this.getParent(key);
        
        if (!parent) {
            return path;
        }
        
        path.push(parent);
        let currentKey = parent;
        
        while (parent) {
            parent = this.getParent(currentKey);
            if (parent) {
                path.push(parent);
                currentKey = parent;
            }
        }
        
        path.reverse();
        return path;
    }

    public getChildBadge(key: RBadge.Key): RBadge.Key[] {
        const result = [key];
        const children = RBadge.Tree[key];
        
        children?.forEach(childKey => {
            result.push(...this.getChildBadge(childKey));
        });
        
        return result;
    }

    public updatBadge(): void {
        Time.timeDelay.cancelBy(this.sTimer);
        this.sTimer = Time.delay(0.5, () => {
            Notifier.send(ListenID.Badge_Update);
        }).id;
    }

    public onLogin_Finish(): void {
        this.getItem(RBadge.Key.Shop_FreeCoin);
    }
}
